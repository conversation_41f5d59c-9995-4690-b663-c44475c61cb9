using MQTTnet;
using MQTTnet.Client;
using MQTTnet.Protocol;
using Serilog;
using System.Text;

namespace MqttCameraRealtimeTest
{
    /// <summary>
    /// 简化的MQTT客户端管理器，专用于实时性测试
    /// </summary>
    public class SimpleMqttManager : IDisposable
    {
        #region 私有字段
        private IMqttClient? _mqttClient;
        private bool _disposed = false;
        private readonly Dictionary<string, MqttQualityOfServiceLevel> _subscriptions = new();
        #endregion

        #region 配置属性
        public string ServerEndpoint { get; set; } = "***************";
        public int ServerPort { get; set; } = 1884;
        public string ClientId { get; set; } = "test_client_001";
        public string Username { get; set; } = "";
        public string Password { get; set; } = "";
        #endregion

        #region 事件
        public event Action<string, string>? MessageReceived;
        public event Action<bool>? ConnectionChanged;
        public event Action<string>? ErrorOccurred;
        #endregion

        #region 属性
        public bool IsConnected => _mqttClient?.IsConnected == true;
        #endregion

        public SimpleMqttManager(string serverEndpoint = "***************", int serverPort = 1884, string clientId = "test_client_001")
        {
            ServerEndpoint = serverEndpoint;
            ServerPort = serverPort;
            ClientId = clientId;
            InitializeClient();
        }

        #region 初始化
        private void InitializeClient()
        {
            try
            {
                _mqttClient = new MqttFactory().CreateMqttClient();
                _mqttClient.ConnectedAsync += OnConnectedAsync;
                _mqttClient.DisconnectedAsync += OnDisconnectedAsync;
                _mqttClient.ApplicationMessageReceivedAsync += OnMessageReceivedAsync;
                Log.Information("MQTT客户端初始化完成");
            }
            catch (Exception ex)
            {
                Log.Error(ex, "初始化MQTT客户端失败");
                ErrorOccurred?.Invoke($"初始化MQTT客户端失败: {ex.Message}");
            }
        }
        #endregion

        #region 连接管理
        public async Task<bool> ConnectAsync()
        {
            if (_disposed)
            {
                Log.Warning("客户端已释放，无法连接");
                return false;
            }

            try
            {
                if (IsConnected)
                    return true;

                Log.Information("开始连接到 {ServerEndpoint}:{ServerPort}，客户端ID: {ClientId}", 
                    ServerEndpoint, ServerPort, ClientId);

                var options = new MqttClientOptionsBuilder()
                    .WithTcpServer(ServerEndpoint, ServerPort)
                    .WithCredentials(Username, Password)
                    .WithClientId(ClientId)
                    .WithCleanSession()
                    .WithKeepAlivePeriod(TimeSpan.FromSeconds(60))
                    .WithTimeout(TimeSpan.FromSeconds(30))
                    .Build();

                var result = await _mqttClient!.ConnectAsync(options);

                if (result.ResultCode == MqttClientConnectResultCode.Success)
                {
                    Log.Information("MQTT连接成功，会话存在: {IsSessionPresent}", result.IsSessionPresent);
                    return true;
                }
                else
                {
                    Log.Error("MQTT连接失败，结果代码: {ResultCode}, 原因: {ReasonString}", 
                        result.ResultCode, result.ReasonString);
                    return false;
                }
            }
            catch (Exception ex)
            {
                Log.Error(ex, "连接MQTT服务器时发生异常");
                ErrorOccurred?.Invoke($"连接失败: {ex.Message}");
                return false;
            }
        }

        public async Task DisconnectAsync()
        {
            if (_mqttClient?.IsConnected == true)
            {
                try
                {
                    await _mqttClient.DisconnectAsync();
                    Log.Information("MQTT连接已断开");
                }
                catch (Exception ex)
                {
                    Log.Error(ex, "断开MQTT连接时发生异常");
                }
            }
        }
        #endregion

        #region 订阅管理
        public async Task<bool> SubscribeAsync(string topic, MqttQualityOfServiceLevel qos = MqttQualityOfServiceLevel.AtLeastOnce)
        {
            if (_disposed || string.IsNullOrWhiteSpace(topic))
                return false;

            try
            {
                _subscriptions[topic] = qos;

                if (!IsConnected)
                    return true; // 连接后会自动订阅

                var subscribeOptions = new MqttClientSubscribeOptionsBuilder()
                    .WithTopicFilter(topic, qos)
                    .Build();

                var result = await _mqttClient!.SubscribeAsync(subscribeOptions);
                var success = result.Items.Count > 0 &&
                       (result.Items.First().ResultCode == MqttClientSubscribeResultCode.GrantedQoS0 ||
                        result.Items.First().ResultCode == MqttClientSubscribeResultCode.GrantedQoS1 ||
                        result.Items.First().ResultCode == MqttClientSubscribeResultCode.GrantedQoS2);

                if (success)
                {
                    Log.Information("成功订阅主题: {Topic}", topic);
                }
                else
                {
                    Log.Warning("订阅主题失败: {Topic}", topic);
                }

                return success;
            }
            catch (Exception ex)
            {
                Log.Error(ex, "订阅主题失败 {Topic}", topic);
                ErrorOccurred?.Invoke($"订阅主题失败 {topic}: {ex.Message}");
                return false;
            }
        }
        #endregion

        #region 事件处理
        private async Task OnConnectedAsync(MqttClientConnectedEventArgs e)
        {
            Log.Information("MQTT客户端已连接");
            ConnectionChanged?.Invoke(true);

            // 重新订阅所有主题
            foreach (var subscription in _subscriptions)
            {
                await SubscribeAsync(subscription.Key, subscription.Value);
            }
        }

        private Task OnDisconnectedAsync(MqttClientDisconnectedEventArgs e)
        {
            Log.Warning("MQTT客户端已断开连接，原因: {Reason}", e.Reason);
            ConnectionChanged?.Invoke(false);
            return Task.CompletedTask;
        }

        private Task OnMessageReceivedAsync(MqttApplicationMessageReceivedEventArgs e)
        {
            try
            {
                var topic = e.ApplicationMessage.Topic;
                var message = Encoding.UTF8.GetString(e.ApplicationMessage.PayloadSegment);
                
                Log.Debug("收到MQTT消息 - 主题: {Topic}, 内容: {Message}", topic, message);
                MessageReceived?.Invoke(topic, message);
            }
            catch (Exception ex)
            {
                Log.Error(ex, "处理MQTT消息时发生异常");
            }
            return Task.CompletedTask;
        }
        #endregion

        #region 消息发送
        public async Task<bool> PublishAsync(string topic, string message, 
            MqttQualityOfServiceLevel qos = MqttQualityOfServiceLevel.AtLeastOnce, bool retain = false)
        {
            if (_disposed || string.IsNullOrWhiteSpace(topic) || !IsConnected)
                return false;

            try
            {
                var mqttMessage = new MqttApplicationMessageBuilder()
                    .WithTopic(topic)
                    .WithPayload(Encoding.UTF8.GetBytes(message ?? ""))
                    .WithQualityOfServiceLevel(qos)
                    .WithRetainFlag(retain)
                    .Build();

                var result = await _mqttClient!.PublishAsync(mqttMessage);
                return result.IsSuccess;
            }
            catch (Exception ex)
            {
                Log.Error(ex, "发送MQTT消息失败");
                ErrorOccurred?.Invoke($"发送消息失败: {ex.Message}");
                return false;
            }
        }
        #endregion

        public void Dispose()
        {
            if (!_disposed)
            {
                _disposed = true;
                _mqttClient?.Dispose();
                Log.Information("MQTT管理器已释放");
            }
        }
    }
}
