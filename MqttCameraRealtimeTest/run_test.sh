#!/bin/bash

echo "========================================"
echo "MQTT相机实时性测试工具"
echo "========================================"
echo

# 检查.NET是否安装
if ! command -v dotnet &> /dev/null; then
    echo "错误: 未找到.NET运行时，请先安装.NET 8.0或更高版本"
    exit 1
fi

# 编译项目
echo "正在编译项目..."
dotnet build --configuration Release
if [ $? -ne 0 ]; then
    echo "编译失败，请检查错误信息"
    exit 1
fi

echo "编译成功！"
echo

# 显示使用说明
echo "使用说明:"
echo "1. 默认配置测试 - 直接按回车"
echo "2. 自定义配置 - 输入参数，例如: --mqtt-server 192.168.1.100"
echo "3. 保存图像测试 - 输入: --save-images"
echo "4. 查看帮助 - 输入: --help"
echo

read -p "请输入参数 (直接回车使用默认配置): " params

echo
echo "启动测试程序..."
echo "========================================"

if [ -z "$params" ]; then
    dotnet run --configuration Release
else
    dotnet run --configuration Release -- $params
fi

echo
echo "========================================"
echo "测试程序已退出"
