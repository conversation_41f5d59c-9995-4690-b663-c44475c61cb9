using Serilog;
using System.Text;

namespace MqttCameraRealtimeTest
{
    class Program
    {
        private static SimpleMqttManager? _mqttManager;
        private static SimpleCameraManager? _cameraManager;
        private static RealtimeTestManager? _testManager;

        static async Task Main(string[] args)
        {
            // 配置日志
            ConfigureLogging();

            try
            {
                Log.Information("=== MQTT相机实时性测试工具启动 ===");
                
                // 解析命令行参数
                var config = ParseArguments(args);
                
                // 初始化组件
                if (!await InitializeComponentsAsync(config))
                {
                    Log.Error("组件初始化失败");
                    return;
                }

                // 启动测试
                if (!await StartTestAsync())
                {
                    Log.Error("启动测试失败");
                    return;
                }

                // 等待用户输入
                await RunInteractiveMode();
            }
            catch (Exception ex)
            {
                Log.Fatal(ex, "程序运行时发生致命错误");
            }
            finally
            {
                await CleanupAsync();
                Log.Information("=== 程序已退出 ===");
                Log.CloseAndFlush();
            }
        }

        #region 配置和初始化
        private static void ConfigureLogging()
        {
            Log.Logger = new LoggerConfiguration()
                .MinimumLevel.Debug()
                .WriteTo.Console(outputTemplate: "[{Timestamp:HH:mm:ss} {Level:u3}] {Message:lj}{NewLine}{Exception}")
                .WriteTo.File("logs/test-.log", 
                    rollingInterval: RollingInterval.Day,
                    outputTemplate: "[{Timestamp:yyyy-MM-dd HH:mm:ss.fff} {Level:u3}] {Message:lj}{NewLine}{Exception}")
                .CreateLogger();
        }

        private static TestConfig ParseArguments(string[] args)
        {
            var config = new TestConfig();

            for (int i = 0; i < args.Length; i++)
            {
                switch (args[i].ToLower())
                {
                    case "--mqtt-server":
                        if (i + 1 < args.Length) config.MqttServer = args[++i];
                        break;
                    case "--mqtt-port":
                        if (i + 1 < args.Length && int.TryParse(args[++i], out int port))
                            config.MqttPort = port;
                        break;
                    case "--mqtt-topic":
                        if (i + 1 < args.Length) config.MqttTopic = args[++i];
                        break;
                    case "--trigger-field":
                        if (i + 1 < args.Length) config.TriggerField = args[++i];
                        break;
                    case "--trigger-value":
                        if (i + 1 < args.Length) config.TriggerValue = args[++i];
                        break;
                    case "--save-images":
                        config.SaveImages = true;
                        break;
                    case "--help":
                        PrintUsage();
                        Environment.Exit(0);
                        break;
                }
            }

            return config;
        }

        private static void PrintUsage()
        {
            Console.WriteLine("MQTT相机实时性测试工具");
            Console.WriteLine("用法: MqttCameraRealtimeTest [选项]");
            Console.WriteLine();
            Console.WriteLine("选项:");
            Console.WriteLine("  --mqtt-server <地址>     MQTT服务器地址 (默认: 192.168.104.188)");
            Console.WriteLine("  --mqtt-port <端口>       MQTT服务器端口 (默认: 1884)");
            Console.WriteLine("  --mqtt-topic <主题>      MQTT订阅主题 (默认: /UploadData)");
            Console.WriteLine("  --trigger-field <字段>   触发字段名 (默认: DI1)");
            Console.WriteLine("  --trigger-value <值>     触发值 (默认: 1)");
            Console.WriteLine("  --save-images           保存测试图像");
            Console.WriteLine("  --help                  显示此帮助信息");
        }

        private static async Task<bool> InitializeComponentsAsync(TestConfig config)
        {
            try
            {
                Log.Information("开始初始化组件...");

                // 初始化MQTT管理器
                _mqttManager = new SimpleMqttManager(config.MqttServer, config.MqttPort, "realtime_test_client");
                _mqttManager.ConnectionChanged += (connected) =>
                {
                    Log.Information("MQTT连接状态: {Status}", connected ? "已连接" : "已断开");
                };
                _mqttManager.ErrorOccurred += (error) =>
                {
                    Log.Warning("MQTT错误: {Error}", error);
                };

                // 连接MQTT
                if (!await _mqttManager.ConnectAsync())
                {
                    Log.Error("MQTT连接失败");
                    return false;
                }

                // 初始化相机管理器
                _cameraManager = new SimpleCameraManager();
                if (!await _cameraManager.InitializeCamerasAsync())
                {
                    Log.Error("相机初始化失败");
                    return false;
                }

                // 初始化测试管理器
                _testManager = new RealtimeTestManager(_mqttManager, _cameraManager)
                {
                    MqttTopic = config.MqttTopic,
                    TriggerField = config.TriggerField,
                    TriggerValue = config.TriggerValue,
                    SaveImages = config.SaveImages
                };

                Log.Information("组件初始化完成");
                return true;
            }
            catch (Exception ex)
            {
                Log.Error(ex, "初始化组件时发生异常");
                return false;
            }
        }

        private static async Task<bool> StartTestAsync()
        {
            try
            {
                if (_testManager == null)
                {
                    Log.Error("测试管理器未初始化");
                    return false;
                }

                return await _testManager.StartTestAsync();
            }
            catch (Exception ex)
            {
                Log.Error(ex, "启动测试时发生异常");
                return false;
            }
        }
        #endregion

        #region 交互模式
        private static async Task RunInteractiveMode()
        {
            Console.WriteLine();
            Console.WriteLine("=== 实时性测试已启动 ===");
            Console.WriteLine("命令:");
            Console.WriteLine("  s - 显示统计信息");
            Console.WriteLine("  r - 重置统计");
            Console.WriteLine("  t - 手动触发测试");
            Console.WriteLine("  q - 退出程序");
            Console.WriteLine();
            Console.WriteLine("等待MQTT触发消息...");
            Console.WriteLine();

            while (true)
            {
                var key = Console.ReadKey(true);
                
                switch (key.KeyChar)
                {
                    case 's':
                    case 'S':
                        ShowStatistics();
                        break;
                    case 'r':
                    case 'R':
                        ResetStatistics();
                        break;
                    case 't':
                    case 'T':
                        await ManualTriggerTest();
                        break;
                    case 'q':
                    case 'Q':
                        Console.WriteLine("正在退出...");
                        return;
                }
            }
        }

        private static void ShowStatistics()
        {
            if (_testManager == null) return;

            var stats = _testManager.GetStatistics();
            
            Console.WriteLine();
            Console.WriteLine("=== 当前统计信息 ===");
            Console.WriteLine($"总测试次数: {stats.TotalTests}");
            Console.WriteLine($"成功次数: {stats.SuccessCount}");
            Console.WriteLine($"失败次数: {stats.FailureCount}");
            Console.WriteLine($"成功率: {stats.SuccessRate:P1}");
            
            if (stats.SuccessCount > 0)
            {
                Console.WriteLine($"平均延迟: {stats.AverageLatencyMs:F1}ms");
                Console.WriteLine($"最小延迟: {stats.MinLatencyMs:F1}ms");
                Console.WriteLine($"最大延迟: {stats.MaxLatencyMs:F1}ms");
                Console.WriteLine($"延迟标准差: {stats.LatencyStandardDeviationMs:F1}ms");
                Console.WriteLine($"平均拍照耗时: {stats.AveragePhotoCaptureTimeMs:F1}ms");
            }
            Console.WriteLine("==================");
            Console.WriteLine();
        }

        private static void ResetStatistics()
        {
            // 这里可以添加重置统计的功能
            Console.WriteLine("统计信息重置功能待实现");
        }

        private static async Task ManualTriggerTest()
        {
            if (_cameraManager == null) return;

            Console.WriteLine("执行手动拍照测试...");
            
            var stopwatch = System.Diagnostics.Stopwatch.StartNew();
            var (image1, image2, captureTime) = await _cameraManager.TakePhotoAsync();
            stopwatch.Stop();

            if (image1 != null && image2 != null)
            {
                Console.WriteLine($"手动测试成功 - 总耗时: {stopwatch.Elapsed.TotalMilliseconds:F1}ms, " +
                                $"拍照耗时: {captureTime:F1}ms");
            }
            else
            {
                Console.WriteLine("手动测试失败 - 无法获取图像");
            }
        }
        #endregion

        #region 清理
        private static async Task CleanupAsync()
        {
            try
            {
                _testManager?.Dispose();
                
                if (_mqttManager != null)
                {
                    await _mqttManager.DisconnectAsync();
                    _mqttManager.Dispose();
                }
                
                _cameraManager?.Dispose();
            }
            catch (Exception ex)
            {
                Log.Warning(ex, "清理资源时发生异常");
            }
        }
        #endregion
    }

    /// <summary>
    /// 测试配置
    /// </summary>
    public class TestConfig
    {
        public string MqttServer { get; set; } = "192.168.104.188";
        public int MqttPort { get; set; } = 1884;
        public string MqttTopic { get; set; } = "/UploadData";
        public string TriggerField { get; set; } = "DI1";
        public string TriggerValue { get; set; } = "1";
        public bool SaveImages { get; set; } = false;
    }
}
