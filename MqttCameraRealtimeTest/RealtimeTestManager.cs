using MvCameraControl;
using Newtonsoft.Json;
using Serilog;
using System.Diagnostics;

namespace MqttCameraRealtimeTest
{
    /// <summary>
    /// 测试结果数据
    /// </summary>
    public class TestResult
    {
        public string TestId { get; set; } = "";
        public DateTime MessageReceiveTime { get; set; }
        public DateTime PhotoCompleteTime { get; set; }
        public double TotalLatencyMs { get; set; }
        public double PhotoCaptureTimeMs { get; set; }
        public double PhotoProcessTimeMs { get; set; }
        public bool Success { get; set; }
    }

    /// <summary>
    /// 性能统计类
    /// </summary>
    public class PerformanceStatistics
    {
        private readonly List<TestResult> _testResults = new();
        private readonly object _lock = new();

        public void AddTestResult(TestResult result)
        {
            lock (_lock)
            {
                _testResults.Add(result);
            }
        }

        public StatisticsSummary GetCurrentStatistics()
        {
            lock (_lock)
            {
                if (_testResults.Count == 0)
                {
                    return new StatisticsSummary();
                }

                var successfulTests = _testResults.Where(r => r.Success).ToList();
                var latencies = successfulTests.Select(r => r.TotalLatencyMs).ToList();
                var photoTimes = successfulTests.Select(r => r.PhotoCaptureTimeMs).ToList();

                return new StatisticsSummary
                {
                    TotalTests = _testResults.Count,
                    SuccessCount = successfulTests.Count,
                    FailureCount = _testResults.Count - successfulTests.Count,
                    SuccessRate = successfulTests.Count / (double)_testResults.Count,
                    AverageLatencyMs = latencies.Count > 0 ? latencies.Average() : 0,
                    MinLatencyMs = latencies.Count > 0 ? latencies.Min() : 0,
                    MaxLatencyMs = latencies.Count > 0 ? latencies.Max() : 0,
                    LatencyStandardDeviationMs = CalculateStandardDeviation(latencies),
                    AveragePhotoCaptureTimeMs = photoTimes.Count > 0 ? photoTimes.Average() : 0
                };
            }
        }

        private static double CalculateStandardDeviation(List<double> values)
        {
            if (values.Count <= 1) return 0;

            var average = values.Average();
            var sumOfSquaresOfDifferences = values.Select(val => (val - average) * (val - average)).Sum();
            return Math.Sqrt(sumOfSquaresOfDifferences / values.Count);
        }

        public class StatisticsSummary
        {
            public int TotalTests { get; set; }
            public int SuccessCount { get; set; }
            public int FailureCount { get; set; }
            public double SuccessRate { get; set; }
            public double AverageLatencyMs { get; set; }
            public double MinLatencyMs { get; set; }
            public double MaxLatencyMs { get; set; }
            public double LatencyStandardDeviationMs { get; set; }
            public double AveragePhotoCaptureTimeMs { get; set; }
        }
    }

    /// <summary>
    /// 实时性测试管理器
    /// </summary>
    public class RealtimeTestManager : IDisposable
    {
        #region 私有字段
        private readonly SimpleMqttManager _mqttManager;
        private readonly SimpleCameraManager _cameraManager;
        private readonly PerformanceStatistics _statistics;
        private bool _disposed = false;
        private bool _testRunning = false;
        #endregion

        #region 配置属性
        public string MqttTopic { get; set; } = "/UploadData";
        public string TriggerField { get; set; } = "DI1";
        public string TriggerValue { get; set; } = "1";
        public bool SaveImages { get; set; } = true;
        public string ImageSaveDirectory { get; set; } = "TestImages";
        #endregion

        public RealtimeTestManager(SimpleMqttManager mqttManager, SimpleCameraManager cameraManager)
        {
            _mqttManager = mqttManager ?? throw new ArgumentNullException(nameof(mqttManager));
            _cameraManager = cameraManager ?? throw new ArgumentNullException(nameof(cameraManager));
            _statistics = new PerformanceStatistics();

            // 确保图像保存目录存在
            if (SaveImages && !Directory.Exists(ImageSaveDirectory))
            {
                Directory.CreateDirectory(ImageSaveDirectory);
            }

            Log.Information("实时性测试管理器已初始化");
        }

        #region 测试控制
        public async Task<bool> StartTestAsync()
        {
            if (_testRunning)
            {
                Log.Warning("测试已在运行中");
                return false;
            }

            try
            {
                Log.Information("开始实时性测试");
                _testRunning = true;

                // 订阅MQTT主题
                _mqttManager.MessageReceived += OnMqttMessageReceived;
                var subscribed = await _mqttManager.SubscribeAsync(MqttTopic);
                
                if (!subscribed)
                {
                    Log.Error("订阅MQTT主题失败: {Topic}", MqttTopic);
                    return false;
                }

                Log.Information("实时性测试已启动，监听主题: {Topic}", MqttTopic);
                return true;
            }
            catch (Exception ex)
            {
                Log.Error(ex, "启动实时性测试失败");
                _testRunning = false;
                return false;
            }
        }

        public void StopTest()
        {
            if (!_testRunning)
                return;

            _testRunning = false;
            _mqttManager.MessageReceived -= OnMqttMessageReceived;
            
            Log.Information("实时性测试已停止");
            PrintStatistics();
        }
        #endregion

        #region MQTT消息处理
        private void OnMqttMessageReceived(string topic, string message)
        {
            if (!_testRunning)
                return;

            try
            {
                // 记录消息接收时间
                var messageReceiveTime = DateTime.Now;
                var stopwatch = Stopwatch.StartNew();

                Log.Debug("收到MQTT消息 - 主题: {Topic}, 内容: {Message}", topic, message);

                // 解析JSON消息
                if (!TryParseMessage(message, out var triggerDetected))
                {
                    Log.Debug("消息解析失败或不包含触发条件");
                    return;
                }

                if (!triggerDetected)
                {
                    Log.Debug("消息不包含触发条件: {Field}={Value}", TriggerField, TriggerValue);
                    return;
                }

                Log.Information("检测到触发条件，开始拍照测试");

                // 执行拍照测试
                _ = Task.Run(() => ExecutePhotoTest(messageReceiveTime, stopwatch));
            }
            catch (Exception ex)
            {
                Log.Error(ex, "处理MQTT消息时发生异常");
            }
        }

        private bool TryParseMessage(string message, out bool triggerDetected)
        {
            triggerDetected = false;

            try
            {
                var json = JsonConvert.DeserializeObject<dynamic>(message);
                if (json == null)
                    return false;

                // 检查是否包含触发字段和值
                var fieldValue = json[TriggerField]?.ToString();
                triggerDetected = fieldValue == TriggerValue;
                
                return true;
            }
            catch (Exception ex)
            {
                Log.Debug(ex, "解析JSON消息失败");
                return false;
            }
        }
        #endregion

        #region 拍照测试
        private async Task ExecutePhotoTest(DateTime messageReceiveTime, Stopwatch totalStopwatch)
        {
            try
            {
                var testId = Guid.NewGuid().ToString("N")[..8];
                Log.Information("开始执行拍照测试 ID: {TestId}", testId);

                // 拍照
                var photoStopwatch = Stopwatch.StartNew();
                var (image1, image2, captureTime) = await _cameraManager.TakePhotoAsync();
                photoStopwatch.Stop();

                var photoCompleteTime = DateTime.Now;
                totalStopwatch.Stop();

                // 记录性能数据
                var testResult = new TestResult
                {
                    TestId = testId,
                    MessageReceiveTime = messageReceiveTime,
                    PhotoCompleteTime = photoCompleteTime,
                    TotalLatencyMs = totalStopwatch.Elapsed.TotalMilliseconds,
                    PhotoCaptureTimeMs = captureTime,
                    PhotoProcessTimeMs = photoStopwatch.Elapsed.TotalMilliseconds,
                    Success = image1 != null && image2 != null
                };

                _statistics.AddTestResult(testResult);

                if (testResult.Success)
                {
                    Log.Information("拍照测试成功 ID: {TestId}, 总延迟: {TotalLatency:F2}ms, " +
                                  "拍照耗时: {PhotoTime:F2}ms", 
                                  testId, testResult.TotalLatencyMs, testResult.PhotoCaptureTimeMs);

                    // 保存图像（如果启用）
                    if (SaveImages)
                    {
                        await SaveTestImagesAsync(testId, image1!, image2!, testResult);
                    }
                }
                else
                {
                    Log.Warning("拍照测试失败 ID: {TestId}, 总延迟: {TotalLatency:F2}ms", 
                              testId, testResult.TotalLatencyMs);
                }

                // 打印实时统计
                PrintRealtimeStatistics();
            }
            catch (Exception ex)
            {
                Log.Error(ex, "执行拍照测试时发生异常");
            }
        }

        private async Task SaveTestImagesAsync(string testId, IImage image1, IImage image2, TestResult testResult)
        {
            try
            {
                var timestamp = testResult.MessageReceiveTime.ToString("yyyyMMdd_HHmmss_fff");
                var fileName1 = Path.Combine(ImageSaveDirectory, $"{timestamp}_{testId}_camera1.bmp");
                var fileName2 = Path.Combine(ImageSaveDirectory, $"{timestamp}_{testId}_camera2.bmp");

                // 保存图像（这里需要根据实际的IImage接口实现）
                // 注意：这是一个简化的示例，实际实现需要根据MvCameraControl的API
                await Task.Run(() =>
                {
                    // 假设IImage有保存方法，实际需要根据SDK文档实现
                    // image1.Save(fileName1);
                    // image2.Save(fileName2);
                    
                    Log.Debug("图像已保存: {FileName1}, {FileName2}", fileName1, fileName2);
                });

                // 保存测试结果到JSON文件
                var resultFileName = Path.Combine(ImageSaveDirectory, $"{timestamp}_{testId}_result.json");
                var resultJson = JsonConvert.SerializeObject(testResult, Formatting.Indented);
                await File.WriteAllTextAsync(resultFileName, resultJson);
            }
            catch (Exception ex)
            {
                Log.Warning(ex, "保存测试图像时发生异常");
            }
        }
        #endregion

        #region 统计和报告
        private void PrintRealtimeStatistics()
        {
            var stats = _statistics.GetCurrentStatistics();
            
            Console.WriteLine($"\r实时统计 - 测试次数: {stats.TotalTests}, " +
                            $"成功率: {stats.SuccessRate:P1}, " +
                            $"平均延迟: {stats.AverageLatencyMs:F1}ms, " +
                            $"最大延迟: {stats.MaxLatencyMs:F1}ms");
        }

        private void PrintStatistics()
        {
            var stats = _statistics.GetCurrentStatistics();
            
            Log.Information("=== 实时性测试统计报告 ===");
            Log.Information("总测试次数: {TotalTests}", stats.TotalTests);
            Log.Information("成功次数: {SuccessCount}", stats.SuccessCount);
            Log.Information("失败次数: {FailureCount}", stats.FailureCount);
            Log.Information("成功率: {SuccessRate:P2}", stats.SuccessRate);
            Log.Information("平均总延迟: {AverageLatency:F2}ms", stats.AverageLatencyMs);
            Log.Information("最小延迟: {MinLatency:F2}ms", stats.MinLatencyMs);
            Log.Information("最大延迟: {MaxLatency:F2}ms", stats.MaxLatencyMs);
            Log.Information("延迟标准差: {LatencyStdDev:F2}ms", stats.LatencyStandardDeviationMs);
            Log.Information("平均拍照耗时: {AveragePhotoTime:F2}ms", stats.AveragePhotoCaptureTimeMs);
            Log.Information("========================");
        }

        public PerformanceStatistics.StatisticsSummary GetStatistics()
        {
            return _statistics.GetCurrentStatistics();
        }
        #endregion

        public void Dispose()
        {
            if (_disposed)
                return;

            _disposed = true;
            StopTest();
            Log.Information("实时性测试管理器已释放");
        }
    }
}
