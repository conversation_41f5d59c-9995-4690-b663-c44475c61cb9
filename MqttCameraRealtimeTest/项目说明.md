# MQTT相机实时性测试工具 - 项目说明

## 项目概述

这是一个从原始AOI项目中抽离出来的专用测试工具，专门用于测试MQTT消息接收到相机拍照完成的实时性。该工具简化了原项目的复杂功能，专注于性能测试和实时性分析。

## 抽离的功能模块

### 1. MQTT管理器 (SimpleMqttManager.cs)
- **原始来源**: `GGEC.AOI.T68ZJ/Managers/SimpleMqttManager.cs`
- **简化内容**:
  - 移除了复杂的重连逻辑
  - 简化了错误处理
  - 专注于基本的连接、订阅、消息接收功能
  - 保留了核心的MQTT通信能力

### 2. 相机管理器 (SimpleCameraManager.cs)
- **原始来源**: `GGEC.AOI.T68ZJ/Managers/CameraManager.cs`
- **简化内容**:
  - 移除了复杂的预览功能
  - 简化了设备配置逻辑
  - 专注于双相机同步拍照
  - 保留了图像缓存和采集线程

### 3. 实时性测试管理器 (RealtimeTestManager.cs)
- **新增功能**: 专门为实时性测试设计
- **核心功能**:
  - MQTT消息触发检测
  - 精确的时间测量
  - 性能统计分析
  - 测试结果记录

## 技术架构

```
Program.cs (主程序)
├── SimpleMqttManager (MQTT通信)
├── SimpleCameraManager (相机控制)
└── RealtimeTestManager (测试逻辑)
    ├── PerformanceStatistics (性能统计)
    └── TestResult (测试结果)
```

## 测试流程

1. **初始化阶段**
   - 连接MQTT服务器
   - 初始化相机设备
   - 启动图像采集线程

2. **监听阶段**
   - 订阅指定MQTT主题
   - 等待触发消息

3. **测试阶段**
   - 接收MQTT消息 → 开始计时
   - 解析JSON消息检查触发条件
   - 执行双相机拍照
   - 记录完成时间

4. **统计阶段**
   - 计算总延迟时间
   - 更新性能统计
   - 实时显示结果

## 性能指标

- **总延迟时间**: 从MQTT消息接收到拍照完成
- **拍照耗时**: 相机拍照操作的实际时间
- **成功率**: 成功拍照的比例
- **统计分析**: 平均值、最大值、最小值、标准差

## 配置参数

### MQTT配置
- 服务器地址: 192.168.104.188 (默认)
- 端口: 1884 (默认)
- 主题: /UploadData (默认)
- 触发条件: DI1=1 (默认)

### 相机配置
- 曝光时间: 2000μs
- 增益: 20.0
- 像素格式: Mono8
- 最小拍照间隔: 50ms

## 使用场景

### 1. 生产环境性能验证
```bash
dotnet run -- --mqtt-server 192.168.1.100 --save-images
```

### 2. 网络延迟测试
```bash
dotnet run -- --mqtt-server remote.server.com --mqtt-port 1883
```

### 3. 自定义触发条件测试
```bash
dotnet run -- --trigger-field "sensor1" --trigger-value "true"
```

## 与原项目的差异

| 功能 | 原项目 | 测试工具 |
|------|--------|----------|
| 图像处理 | ✅ YOLO推理 | ❌ 已移除 |
| AI检测 | ✅ 三点位检测 | ❌ 已移除 |
| 相机管理 | ✅ 完整功能 | ✅ 简化版本 |
| MQTT通信 | ✅ 完整功能 | ✅ 简化版本 |
| 实时性测试 | ❌ 无专门功能 | ✅ 核心功能 |
| 性能统计 | ❌ 基础日志 | ✅ 详细统计 |
| 用户界面 | ✅ WinForms | ✅ 控制台 |

## 文件结构

```
MqttCameraRealtimeTest/
├── MqttCameraRealtimeTest.csproj    # 项目文件
├── Program.cs                       # 主程序入口
├── SimpleMqttManager.cs             # MQTT管理器
├── SimpleCameraManager.cs           # 相机管理器
├── RealtimeTestManager.cs           # 测试管理器
├── README.md                        # 英文说明文档
├── 项目说明.md                      # 中文说明文档
├── config.example.json              # 配置文件示例
├── run_test.bat                     # Windows运行脚本
├── run_test.sh                      # Linux/Mac运行脚本
└── logs/                           # 日志目录
    └── test-*.log                  # 日志文件
```

## 依赖包

- **MQTTnet**: MQTT客户端库
- **MvCameraControl.Net**: 海康威视相机SDK
- **Newtonsoft.Json**: JSON处理
- **Serilog**: 日志记录

## 编译和运行

### 编译
```bash
dotnet restore
dotnet build
```

### 运行
```bash
# 使用默认配置
dotnet run

# 使用自定义配置
dotnet run -- --mqtt-server 192.168.1.100 --save-images

# 查看帮助
dotnet run -- --help
```

### 使用脚本
```bash
# Windows
run_test.bat

# Linux/Mac
./run_test.sh
```

## 测试结果

程序会生成以下输出：

1. **控制台实时显示**
   - 连接状态
   - 测试结果
   - 性能统计

2. **日志文件**
   - 详细的执行日志
   - 错误和警告信息

3. **测试图像** (可选)
   - 拍照的原始图像
   - 测试结果JSON文件

## 故障排除

### 常见问题
1. **相机初始化失败**: 检查相机连接和驱动
2. **MQTT连接失败**: 验证服务器地址和网络
3. **无法接收消息**: 检查主题和消息格式

### 调试建议
- 查看日志文件获取详细信息
- 使用手动触发测试功能验证相机
- 检查MQTT消息格式是否正确

## 扩展建议

1. **添加配置文件支持**: 支持JSON配置文件
2. **增加更多统计指标**: 如延迟分布图
3. **支持多种触发模式**: 定时触发、手动触发等
4. **添加Web界面**: 远程监控和控制
5. **支持多相机**: 扩展到更多相机设备

## 总结

这个测试工具成功地从复杂的AOI系统中抽离出了核心的MQTT和相机功能，专注于实时性测试。它提供了精确的性能测量、详细的统计分析和灵活的配置选项，是验证系统实时性能的理想工具。
