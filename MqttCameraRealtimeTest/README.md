# MQTT相机实时性测试工具

这是一个专门用于测试MQTT消息接收到相机拍照完成的实时性测试工具，从原始AOI项目中抽离了MQTT和相机功能。

## 功能特性

- **实时性测试**: 测量从MQTT消息接收到拍照完成的总延迟
- **性能统计**: 提供详细的性能统计信息，包括平均延迟、最大延迟、成功率等
- **双相机支持**: 同时测试两个相机的拍照性能
- **可配置参数**: 支持命令行参数配置MQTT服务器、主题、触发条件等
- **图像保存**: 可选择保存测试图像和结果数据
- **实时监控**: 控制台实时显示测试结果和统计信息

## 系统要求

- .NET 8.0 或更高版本
- 支持MvCameraControl.Net的相机设备
- MQTT服务器访问权限

## 安装和编译

1. 确保已安装.NET 8.0 SDK
2. 克隆或下载项目文件
3. 在项目目录中运行：
   ```bash
   dotnet restore
   dotnet build
   ```

## 使用方法

### 基本用法

```bash
dotnet run
```

使用默认配置启动测试：
- MQTT服务器: 192.168.104.188:1884
- 订阅主题: /UploadData
- 触发条件: DI1=1

### 命令行参数

```bash
dotnet run -- [选项]
```

可用选项：
- `--mqtt-server <地址>`: MQTT服务器地址 (默认: 192.168.104.188)
- `--mqtt-port <端口>`: MQTT服务器端口 (默认: 1884)
- `--mqtt-topic <主题>`: MQTT订阅主题 (默认: /UploadData)
- `--trigger-field <字段>`: 触发字段名 (默认: DI1)
- `--trigger-value <值>`: 触发值 (默认: 1)
- `--save-images`: 保存测试图像
- `--help`: 显示帮助信息

### 示例

```bash
# 使用自定义MQTT服务器
dotnet run -- --mqtt-server 192.168.1.100 --mqtt-port 1883

# 使用自定义触发条件并保存图像
dotnet run -- --trigger-field "sensor1" --trigger-value "true" --save-images

# 使用自定义主题
dotnet run -- --mqtt-topic "/test/trigger"
```

## 交互命令

程序运行后，可以使用以下键盘命令：

- `s`: 显示当前统计信息
- `r`: 重置统计数据
- `t`: 手动触发拍照测试
- `q`: 退出程序

## 测试流程

1. **初始化**: 程序启动时初始化MQTT客户端和相机设备
2. **订阅**: 订阅指定的MQTT主题
3. **监听**: 等待MQTT消息
4. **触发**: 当收到包含指定触发条件的消息时，开始计时
5. **拍照**: 执行双相机拍照
6. **统计**: 记录总延迟时间和拍照耗时
7. **报告**: 实时显示测试结果和统计信息

## 性能指标

测试工具会收集以下性能指标：

- **总延迟时间**: 从MQTT消息接收到拍照完成的总时间
- **拍照耗时**: 相机拍照操作的实际耗时
- **成功率**: 成功拍照的比例
- **统计数据**: 平均值、最大值、最小值、标准差

## 输出文件

当启用`--save-images`选项时，程序会在`TestImages`目录下保存：

- 测试图像文件 (BMP格式)
- 测试结果JSON文件，包含详细的性能数据

文件命名格式：`yyyyMMdd_HHmmss_fff_<testId>_<type>.<ext>`

## 日志

程序会生成详细的日志文件：
- 控制台输出: 实时显示重要信息
- 文件日志: 保存在`logs/`目录下，按日期滚动

## 故障排除

### 常见问题

1. **相机初始化失败**
   - 检查相机设备是否正确连接
   - 确认MvCameraControl.Net驱动已正确安装
   - 检查相机设备权限

2. **MQTT连接失败**
   - 验证MQTT服务器地址和端口
   - 检查网络连接
   - 确认MQTT服务器是否运行

3. **无法接收MQTT消息**
   - 检查订阅的主题是否正确
   - 验证消息格式是否为有效JSON
   - 确认触发字段和值是否匹配

### 调试模式

程序默认启用详细日志记录，可以通过日志文件查看详细的执行信息。

## 技术架构

- **SimpleMqttManager**: 简化的MQTT客户端管理器
- **SimpleCameraManager**: 简化的相机管理器，支持双相机同步拍照
- **RealtimeTestManager**: 实时性测试核心逻辑
- **PerformanceStatistics**: 性能统计和分析

## 与原项目的差异

这个测试工具从原始AOI项目中抽离了以下功能：
- 移除了图像处理和AI推理功能
- 简化了相机配置和管理
- 专注于MQTT到拍照的实时性测试
- 添加了详细的性能监控和统计功能

## 许可证

本项目遵循与原项目相同的许可证。
