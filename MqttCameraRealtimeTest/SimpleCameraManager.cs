using MvCameraControl;
using Serilog;
using System.Diagnostics;

namespace MqttCameraRealtimeTest
{
    /// <summary>
    /// 简化的相机管理器，专用于实时性测试
    /// </summary>
    public class SimpleCameraManager : IDisposable
    {
        #region 私有字段
        private readonly List<IDeviceInfo> _deviceInfos = new();
        private readonly List<IDevice> _devices = new();
        private readonly object _syncLock = new();
        private bool _disposed = false;

        // 图像缓存
        private IImage? _cachedImage1;
        private IImage? _cachedImage2;
        private DateTime _lastCacheTime = DateTime.MinValue;

        // 采集线程
        private Thread? _grabThread;
        private bool _grabThreadRunning = false;
        private CancellationTokenSource? _cancellationTokenSource;
        #endregion

        #region 配置属性
        public double ExposureTime { get; set; } = 2000.0; // 曝光时间（微秒）
        public double Gain { get; set; } = 20.0; // 增益值
        public int MinPhotoIntervalMs { get; set; } = 50; // 最小拍照间隔（毫秒）
        public string PixelFormat { get; set; } = "Mono8"; // 像素格式
        #endregion

        #region 属性
        public bool IsInitialized => _devices.Count >= 2;
        public int DeviceCount => _devices.Count;
        #endregion

        public SimpleCameraManager()
        {
            Log.Information("简化相机管理器初始化");
        }

        #region 初始化
        public async Task<bool> InitializeCamerasAsync()
        {
            try
            {
                Log.Information("开始初始化相机");

                // 枚举设备
                int nRet = DeviceEnumerator.EnumDevices(DeviceTLayerType.MvGigEDevice, out List<IDeviceInfo> deviceList);
                if (nRet != 0 || deviceList.Count == 0)
                {
                    Log.Warning("未找到任何相机设备");
                    return false;
                }

                Log.Information("找到 {Count} 个相机设备", deviceList.Count);

                // 初始化前两个设备
                int initCount = Math.Min(2, deviceList.Count);
                for (int i = 0; i < initCount; i++)
                {
                    var deviceInfo = deviceList[i];
                    _deviceInfos.Add(deviceInfo);

                    if (!await InitializeDeviceAsync(deviceInfo, i))
                    {
                        Log.Error("初始化设备 {Index} 失败", i);
                        return false;
                    }
                }

                // 启动图像采集线程
                if (_devices.Count >= 2)
                {
                    StartImageGrabThread();
                }

                Log.Information("相机初始化完成，成功初始化 {Count} 个设备", _devices.Count);
                return true;
            }
            catch (Exception ex)
            {
                Log.Error(ex, "相机初始化失败");
                return false;
            }
        }

        private Task<bool> InitializeDeviceAsync(IDeviceInfo deviceInfo, int index)
        {
            try
            {
                Log.Information("初始化设备 {Index}: {SerialNumber}", index, deviceInfo.SerialNumber);

                var device = DeviceFactory.CreateDevice(deviceInfo);
                if (device == null)
                {
                    Log.Error("创建设备失败: {SerialNumber}", deviceInfo.SerialNumber);
                    return Task.FromResult(false);
                }

                int result = device.Open();
                if (result != 0)
                {
                    Log.Error("打开设备失败: {SerialNumber}, 错误码: {ErrorCode:X8}",
                        deviceInfo.SerialNumber, result);
                    device.Dispose();
                    return Task.FromResult(false);
                }

                // 配置设备参数
                ConfigureDevice(device);

                // 开始采集
                device.StreamGrabber.SetOutputQueueSize(3);
                device.StreamGrabber.SetImageNodeNum(5);
                result = device.StreamGrabber.StartGrabbing(StreamGrabStrategy.LatestImages);
                if (result != 0)
                {
                    Log.Error("开始采集失败: {SerialNumber}, 错误码: {ErrorCode:X8}",
                        deviceInfo.SerialNumber, result);
                    device.Close();
                    device.Dispose();
                    return Task.FromResult(false);
                }

                _devices.Add(device);
                Log.Information("设备 {Index} 初始化成功: {SerialNumber}", index, deviceInfo.SerialNumber);
                return Task.FromResult(true);
            }
            catch (Exception ex)
            {
                Log.Error(ex, "初始化设备 {Index} 时发生异常", index);
                return Task.FromResult(false);
            }
        }

        private void ConfigureDevice(IDevice device)
        {
            try
            {
                // 设置像素格式
                device.Parameters.SetEnumValueByString("PixelFormat", PixelFormat);

                // 设置曝光时间
                device.Parameters.SetFloatValue("ExposureTime", (float)ExposureTime);

                // 设置增益
                device.Parameters.SetFloatValue("Gain", (float)Gain);

                // 设置采集模式为连续
                device.Parameters.SetEnumValueByString("AcquisitionMode", "Continuous");

                // 设置触发模式为关闭（自由运行）
                device.Parameters.SetEnumValueByString("TriggerMode", "Off");

                Log.Debug("设备参数配置完成 - 曝光: {ExposureTime}μs, 增益: {Gain}, 格式: {PixelFormat}", 
                    ExposureTime, Gain, PixelFormat);
            }
            catch (Exception ex)
            {
                Log.Warning(ex, "配置设备参数时发生异常，使用默认参数");
            }
        }
        #endregion

        #region 图像采集
        private void StartImageGrabThread()
        {
            lock (_syncLock)
            {
                if (_grabThreadRunning)
                    return;

                _cancellationTokenSource = new CancellationTokenSource();
                _grabThread = new Thread(() => ImageGrabWorker(_cancellationTokenSource.Token))
                {
                    Name = "ImageGrabThread",
                    IsBackground = true
                };
                _grabThreadRunning = true;
                _grabThread.Start();

                Log.Information("图像采集线程已启动");
            }
        }

        private void ImageGrabWorker(CancellationToken cancellationToken)
        {
            Log.Information("图像采集线程开始工作");

            while (!cancellationToken.IsCancellationRequested && !_disposed)
            {
                try
                {
                    // 同时从两个相机获取图像
                    var image1 = CaptureImageFromDevice(0);
                    var image2 = CaptureImageFromDevice(1);

                    if (image1 != null && image2 != null)
                    {
                        UpdateImageCache(image1, image2);
                    }
                    else
                    {
                        image1?.Dispose();
                        image2?.Dispose();
                    }

                    // 短暂休眠，避免过度占用CPU
                    Thread.Sleep(10);
                }
                catch (Exception ex)
                {
                    Log.Error(ex, "图像采集线程异常");
                    Thread.Sleep(100);
                }
            }

            Log.Information("图像采集线程已停止");
        }

        private IImage? CaptureImageFromDevice(int deviceIndex)
        {
            if (deviceIndex >= _devices.Count)
                return null;

            try
            {
                var device = _devices[deviceIndex];
                int result = device.StreamGrabber.GetImageBuffer(100, out IFrameOut frameOut);

                if (result == 0 && frameOut?.Image != null)
                {
                    var image = frameOut.Image;
                    frameOut.Dispose();
                    return image;
                }
                else
                {
                    frameOut?.Dispose();
                    return null;
                }
            }
            catch (Exception ex)
            {
                Log.Debug(ex, "设备 {DeviceIndex} 获取图像失败", deviceIndex);
                return null;
            }
        }

        private void UpdateImageCache(IImage image1, IImage image2)
        {
            lock (_syncLock)
            {
                // 释放旧图像
                _cachedImage1?.Dispose();
                _cachedImage2?.Dispose();

                // 缓存新图像
                _cachedImage1 = image1;
                _cachedImage2 = image2;
                _lastCacheTime = DateTime.Now;
            }
        }
        #endregion

        #region 拍照接口
        /// <summary>
        /// 获取当前缓存的图像对
        /// </summary>
        public (IImage? image1, IImage? image2, TimeSpan imageAge) GetCachedImages()
        {
            lock (_syncLock)
            {
                var age = DateTime.Now - _lastCacheTime;
                return (_cachedImage1, _cachedImage2, age);
            }
        }

        /// <summary>
        /// 拍照 - 返回当前缓存的图像
        /// </summary>
        public (IImage? image1, IImage? image2, double captureTimeMs) TakePhoto()
        {
            var stopwatch = Stopwatch.StartNew();
            
            var (image1, image2, imageAge) = GetCachedImages();
            
            stopwatch.Stop();
            var captureTime = stopwatch.Elapsed.TotalMilliseconds;

            if (image1 != null && image2 != null)
            {
                Log.Debug("拍照成功，耗时: {CaptureTime:F2}ms, 图像年龄: {ImageAge:F2}ms", 
                    captureTime, imageAge.TotalMilliseconds);
            }
            else
            {
                Log.Warning("拍照失败，无可用图像缓存");
            }

            return (image1, image2, captureTime);
        }

        /// <summary>
        /// 异步拍照
        /// </summary>
        public async Task<(IImage? image1, IImage? image2, double captureTimeMs)> TakePhotoAsync()
        {
            return await Task.Run(() => TakePhoto());
        }
        #endregion

        #region 清理
        private void StopImageGrabThread()
        {
            lock (_syncLock)
            {
                if (!_grabThreadRunning)
                    return;

                _grabThreadRunning = false;
                _cancellationTokenSource?.Cancel();

                try
                {
                    _grabThread?.Join(1000);
                }
                catch (Exception ex)
                {
                    Log.Warning(ex, "停止图像采集线程时发生异常");
                }

                _cancellationTokenSource?.Dispose();
                _cancellationTokenSource = null;
                _grabThread = null;

                Log.Information("图像采集线程已停止");
            }
        }

        public void Dispose()
        {
            if (_disposed)
                return;

            _disposed = true;

            // 停止采集线程
            StopImageGrabThread();

            // 清理图像缓存
            lock (_syncLock)
            {
                _cachedImage1?.Dispose();
                _cachedImage2?.Dispose();
                _cachedImage1 = null;
                _cachedImage2 = null;
            }

            // 清理设备
            foreach (var device in _devices)
            {
                try
                {
                    device.StreamGrabber.StopGrabbing();
                    device.Close();
                    device.Dispose();
                }
                catch (Exception ex)
                {
                    Log.Warning(ex, "清理设备时发生异常");
                }
            }

            _devices.Clear();
            _deviceInfos.Clear();

            Log.Information("简化相机管理器已释放");
        }
        #endregion
    }
}
