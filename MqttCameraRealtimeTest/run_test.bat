@echo off
echo ========================================
echo MQTT相机实时性测试工具
echo ========================================
echo.

REM 检查.NET是否安装
dotnet --version >nul 2>&1
if %errorlevel% neq 0 (
    echo 错误: 未找到.NET运行时，请先安装.NET 8.0或更高版本
    pause
    exit /b 1
)

REM 编译项目
echo 正在编译项目...
dotnet build --configuration Release
if %errorlevel% neq 0 (
    echo 编译失败，请检查错误信息
    pause
    exit /b 1
)

echo 编译成功！
echo.

REM 显示使用说明
echo 使用说明:
echo 1. 默认配置测试 - 直接按回车
echo 2. 自定义配置 - 输入参数，例如: --mqtt-server 192.168.1.100
echo 3. 保存图像测试 - 输入: --save-images
echo 4. 查看帮助 - 输入: --help
echo.

set /p "params=请输入参数 (直接回车使用默认配置): "

echo.
echo 启动测试程序...
echo ========================================

if "%params%"=="" (
    dotnet run --configuration Release
) else (
    dotnet run --configuration Release -- %params%
)

echo.
echo ========================================
echo 测试程序已退出
pause
